# 🏗️ Maven 多模块项目支持

## 🎯 功能概述

VSCode Tomcat Manager 现在支持 Maven 多模块项目的智能构建和部署。当检测到项目是 Maven 多模块项目的子模块时，插件会自动：

1. **查找根项目**：向上查找包含当前模块的父 pom.xml
2. **智能构建**：在根项目执行构建，只构建目标模块及其依赖
3. **依赖解析**：确保所有依赖模块都被正确构建

## 🔍 检测逻辑

### 多模块项目检测流程

```
1. 检查当前项目是否为Maven项目
2. 向上查找父目录中的pom.xml文件
3. 检查父pom.xml是否包含当前模块声明
4. 如果找到，标记为多模块项目
5. 使用特殊的构建策略
```

### 示例项目结构

```
fs-paas-workflow/                    # 根项目
├── pom.xml                         # 父pom.xml
├── fs-paas-workflow-api/
├── fs-paas-workflow-core/          # 依赖模块
├── fs-paas-workflow-provider/      # Web模块（依赖core）
│   ├── pom.xml
│   └── src/
└── 其他模块...
```

## 🛠️ 构建策略

### 单模块项目（原有逻辑）

```bash
# 在项目目录执行
cd fs-paas-workflow-provider
mvn clean package
```

### 多模块项目（新逻辑）

```bash
# 在根项目目录执行，安装依赖模块并构建目标模块
cd fs-paas-workflow
mvn clean install -pl fs-paas-workflow-provider -am
```

### Maven 命令参数说明

- `-pl fs-paas-workflow-provider`：只构建指定的模块
- `-am`：同时构建该模块的所有依赖模块（also-make）
- `install`：将构建的模块安装到本地 Maven 仓库，确保依赖可用

## 🔧 技术实现

### 1. 构建策略确定

```typescript
private determineBuildStrategy(config: any): {
  command: string;
  workingDirectory: string;
} {
  // 检查是否为Maven项目
  if (config.build.type === "maven" || config.build.buildCommand.includes("mvn")) {
    const mavenRootPath = this.findMavenRootProject(config.projectPath);

    if (mavenRootPath && mavenRootPath !== config.projectPath) {
      // 多模块项目：在根目录构建指定模块
      const relativePath = path.relative(mavenRootPath, config.projectPath);
      return {
        command: `mvn clean package -pl ${relativePath} -am`,
        workingDirectory: mavenRootPath
      };
    }
  }

  // 单模块项目：使用原有逻辑
  return {
    command: config.build.buildCommand,
    workingDirectory: config.projectPath
  };
}
```

### 2. 根项目查找

```typescript
private findMavenRootProject(projectPath: string): string | null {
  let currentPath = projectPath;
  let parentPomPath = null;

  // 向上查找父pom.xml
  while (currentPath !== path.dirname(currentPath)) {
    const parentPath = path.dirname(currentPath);
    const pomPath = path.join(parentPath, "pom.xml");

    if (fs.existsSync(pomPath)) {
      const pomContent = fs.readFileSync(pomPath, "utf-8");
      const currentDirName = path.basename(currentPath);

      // 检查是否包含当前模块
      if (pomContent.includes(`<module>${currentDirName}</module>`)) {
        parentPomPath = parentPath;
        currentPath = parentPath;
        continue;
      }
    }
    break;
  }

  return parentPomPath;
}
```

## 📋 支持的模块声明格式

插件支持以下 Maven 模块声明格式：

```xml
<!-- 标准格式 -->
<modules>
    <module>fs-paas-workflow-provider</module>
    <module>fs-paas-workflow-core</module>
</modules>

<!-- 相对路径格式 -->
<modules>
    <module>./fs-paas-workflow-provider</module>
    <module>./fs-paas-workflow-core</module>
</modules>
```

## 🎯 解决的问题

### 问题 1：依赖模块未构建

**之前**：只构建 Web 模块，依赖的 core 模块未构建

```bash
cd fs-paas-workflow-provider
mvn clean package  # ❌ 找不到fs-paas-workflow-biz-core
```

**现在**：先安装依赖模块，再构建目标模块

```bash
cd fs-paas-workflow
mvn clean install -pl fs-paas-workflow-provider -am  # ✅ 自动构建并安装依赖模块
```

### 关键修复：package vs install

- **`mvn package`**：只编译和打包，不安装到本地仓库
- **`mvn install`**：编译、打包并安装到本地 Maven 仓库
- **为什么需要 install**：确保依赖模块在本地仓库中可用，避免"dependency not found"错误

### 问题 2：构建顺序错误

**之前**：无法保证依赖模块的构建顺序
**现在**：Maven 自动解析依赖关系，按正确顺序构建

### 问题 3：本地仓库依赖

**之前**：依赖模块可能不在本地仓库中
**现在**：`-am`参数确保依赖模块被安装到本地仓库

## 🧪 测试验证

### 测试场景 1：多模块 Web 项目

```
项目结构：
parent/
├── pom.xml (packaging: pom)
├── core/ (packaging: jar)
└── web/ (packaging: war, 依赖core)

预期结果：
1. 检测到web是多模块项目的子模块
2. 在parent目录执行：mvn clean package -pl web -am
3. 先构建core模块，再构建web模块
4. 成功生成web.war文件
```

### 测试场景 2：单模块项目

```
项目结构：
standalone-web/
├── pom.xml (packaging: war)
└── src/

预期结果：
1. 检测为单模块项目
2. 在standalone-web目录执行：mvn clean package
3. 使用原有构建逻辑
```

## 📈 性能优化

1. **缓存根项目路径**：避免重复查找
2. **智能依赖检测**：只构建必要的依赖模块
3. **并行构建支持**：Maven 自动优化构建顺序

## 🔄 向后兼容

- ✅ 完全兼容现有单模块项目
- ✅ 不影响 Gradle 项目构建
- ✅ 保持原有配置格式
- ✅ 自动检测，无需手动配置

现在您的`fs-paas-workflow-provider`项目应该能够正确构建和部署了！
